package com.cmcc.cmdevops.ci.service.business.impl;

import com.alibaba.fastjson.JSONPath;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cmcc.cmdevops.PageRequest;
import com.cmcc.cmdevops.PageResponse;
import com.cmcc.cmdevops.ci.service.atom.BuildEnvironmentAtomService;
import com.cmcc.cmdevops.ci.service.atom.BuildEnvironmentMonitorAtomService;
import com.cmcc.cmdevops.ci.service.atom.BuildEnvironmentStorageAtomService;
import com.cmcc.cmdevops.ci.service.bo.BuildEnvironmentBO;
import com.cmcc.cmdevops.ci.service.bo.BuildJenkinsNodeBO;
import com.cmcc.cmdevops.ci.service.bo.BuildNodeBO;
import com.cmcc.cmdevops.ci.service.bo.BuildScheduleSnapshotBO;
import com.cmcc.cmdevops.ci.service.bo.CiScheduleRequest;
import com.cmcc.cmdevops.ci.service.business.BuildEnvironmentBizService;
import com.cmcc.cmdevops.ci.service.business.BuildJenkinsNodeBizService;
import com.cmcc.cmdevops.ci.service.business.BuildNodeBizService;
import com.cmcc.cmdevops.ci.service.business.CiScheduleBizService;
import com.cmcc.cmdevops.ci.service.business.util.EmptyValidator;
import com.cmcc.cmdevops.ci.service.business.util.UserUtils;
import com.cmcc.cmdevops.ci.service.dao.BuildEnvironmentDO;
import com.cmcc.cmdevops.ci.service.dao.BuildEnvironmentMonitorDO;
import com.cmcc.cmdevops.exception.BusinessException;
import com.cmcc.cmdevops.util.BeanCloner;
import io.kubernetes.client.custom.IntOrString;
import io.kubernetes.client.custom.Quantity;
import io.kubernetes.client.openapi.ApiClient;
import io.kubernetes.client.openapi.ApiException;
import io.kubernetes.client.openapi.Configuration;
import io.kubernetes.client.openapi.apis.AppsV1Api;
import io.kubernetes.client.openapi.apis.CoreV1Api;
import io.kubernetes.client.openapi.apis.RbacAuthorizationV1Api;
import io.kubernetes.client.openapi.models.RbacV1Subject;
import io.kubernetes.client.openapi.models.V1ConfigMap;
import io.kubernetes.client.openapi.models.V1Container;
import io.kubernetes.client.openapi.models.V1ContainerPort;
import io.kubernetes.client.openapi.models.V1Deployment;
import io.kubernetes.client.openapi.models.V1DeploymentSpec;
import io.kubernetes.client.openapi.models.V1LabelSelector;
import io.kubernetes.client.openapi.models.V1Namespace;
import io.kubernetes.client.openapi.models.V1ObjectMeta;
import io.kubernetes.client.openapi.models.V1PodSpec;
import io.kubernetes.client.openapi.models.V1PodTemplateSpec;
import io.kubernetes.client.openapi.models.V1PolicyRule;
import io.kubernetes.client.openapi.models.V1ResourceRequirements;
import io.kubernetes.client.openapi.models.V1Role;
import io.kubernetes.client.openapi.models.V1RoleBinding;
import io.kubernetes.client.openapi.models.V1RoleRef;
import io.kubernetes.client.openapi.models.V1Service;
import io.kubernetes.client.openapi.models.V1ServiceAccount;
import io.kubernetes.client.openapi.models.V1ServicePort;
import io.kubernetes.client.openapi.models.V1ServiceSpec;
import io.kubernetes.client.util.Config;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.StringReader;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 构建环境业务实现类
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class BuildEnvironmentBizServiceImpl implements BuildEnvironmentBizService {

    @Value("${ci-tool.schedule-image}")
    private String scheduleImage;

    @Value("${ci-tool.tool-image}")
    private String toolImage;

    @Value("${ci-tool.ci-tool-cm.buildkitdToml}")
    private String buildkitdToml;

    @Value("${ci-tool.ci-tool-cm.daemonJson}")
    private String daemonJson;

    private final BuildEnvironmentAtomService buildEnvironmentAtomService;

    private final BuildNodeBizService buildNodeBizService;

    private final BuildJenkinsNodeBizService buildJenkinsNodeBizService;

    private final CiScheduleBizService ciScheduleBizService;

    private final BuildEnvironmentMonitorAtomService buildEnvironmentMonitorAtomService;

    private final BuildEnvironmentStorageAtomService buildEnvironmentStorageAtomService;

    @Override
    public void save(BuildEnvironmentBO buildEnvironmentBO) {
        checkEnvironmentNameUnique(null, buildEnvironmentBO.getEnvironmentName(), buildEnvironmentBO.getSpaceId());
        BuildEnvironmentDO buildEnvironmentDO = BeanCloner.clone(buildEnvironmentBO, BuildEnvironmentDO.class);
        buildEnvironmentDO.setTenantId(UserUtils.getTenantId());
        buildEnvironmentDO.setCreateUid(UserUtils.getUserId());
        buildEnvironmentDO.setSpaceId("");
        buildEnvironmentDO.setProjectAuth(false);
        buildEnvironmentDO.setSpaceAuth(false);
        buildEnvironmentAtomService.save(buildEnvironmentDO);
    }

    @Override
    public void update(BuildEnvironmentBO buildEnvironmentBO) {
        checkEnvironmentNameUnique(buildEnvironmentBO.getId(), buildEnvironmentBO.getEnvironmentName(), buildEnvironmentBO.getSpaceId());
        BuildEnvironmentDO buildEnvironmentDO = BeanCloner.clone(buildEnvironmentBO, BuildEnvironmentDO.class);
        buildEnvironmentDO.setUpdateUid(UserUtils.getUserId());
        buildEnvironmentDO.setSpaceId(buildEnvironmentAtomService.getById(buildEnvironmentBO.getId()).getSpaceId());
        buildEnvironmentAtomService.updateById(buildEnvironmentDO);
    }

    private void checkEnvironmentNameUnique(Integer id, String name, String spaceId) {
        LambdaQueryWrapper<BuildEnvironmentDO> queryWrapper = new LambdaQueryWrapper<>();
        if (EmptyValidator.isNotEmpty(id)) {
            queryWrapper.ne(BuildEnvironmentDO::getId, id);
        }
        queryWrapper.eq(BuildEnvironmentDO::getEnvironmentName, name);
        long count = buildEnvironmentAtomService.count(queryWrapper);
        if (count > 0) {
            throw new BusinessException("已存在该名称的构建环境");
        }
    }

    @Override
    public void delete(Integer id) {
        buildEnvironmentAtomService.removeById(id);
    }

    @Override
    public BuildEnvironmentBO detail(Integer id) {
        BuildEnvironmentDO buildEnvironmentDO = buildEnvironmentAtomService.getById(id);
        return (buildEnvironmentDO != null) ? BeanCloner.clone(buildEnvironmentDO, BuildEnvironmentBO.class) : null;
    }

    @Override
    public PageResponse<List<BuildEnvironmentBO>> page(PageRequest pageRequest, BuildEnvironmentBO buildEnvironmentBO) {
        Page<BuildEnvironmentDO> page = new Page<>(pageRequest.getPageNo(), pageRequest.getPageSize());
        LambdaQueryWrapper<BuildEnvironmentDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.orderByDesc(BuildEnvironmentDO::getCreateTime);
        queryWrapper.eq(BuildEnvironmentDO::getEnvironmentSource, buildEnvironmentBO.getEnvironmentSource());
        if (EmptyValidator.isNotEmpty(buildEnvironmentBO)) {
            if (EmptyValidator.isNotEmpty(buildEnvironmentBO.getEnvironmentName())) {
                queryWrapper.like(BuildEnvironmentDO::getEnvironmentName, buildEnvironmentBO.getEnvironmentName());
            }
            if (EmptyValidator.isNotEmpty(buildEnvironmentBO.getEnvironmentType())) {
                queryWrapper.eq(BuildEnvironmentDO::getEnvironmentType, buildEnvironmentBO.getEnvironmentType());
            }
            if (EmptyValidator.isNotEmpty(buildEnvironmentBO.getAccelerationNode())) {
                queryWrapper.eq(BuildEnvironmentDO::getAccelerationNode, buildEnvironmentBO.getAccelerationNode());
            }
            if (EmptyValidator.isNotEmpty(buildEnvironmentBO.getStatus())) {
                queryWrapper.eq(BuildEnvironmentDO::getStatus, buildEnvironmentBO.getStatus());
            }
//            if (EmptyValidator.isNotEmpty(buildEnvironmentBO.getSpaceId())) {
//                queryWrapper.eq(BuildEnvironmentDO::getSpaceId, buildEnvironmentBO.getSpaceId());
//            }
        }
        Page<BuildEnvironmentDO> result = buildEnvironmentAtomService.page(page, queryWrapper);
        List<BuildEnvironmentBO> list = BeanCloner.clone(result.getRecords(), BuildEnvironmentBO.class);
        return PageResponse.success(list, result.getTotal(), pageRequest.getPageNo(), pageRequest.getPageSize());
    }

    @Override
    public void init(Integer environmentId) {
        ApiClient client = null;
        BuildEnvironmentBO buildEnvironmentBO = this.detail(environmentId);
        String kubeconfigConfig = buildEnvironmentBO.getEnvironmentConfig();
        try {
            client = Config.fromConfig(new StringReader(kubeconfigConfig));
        } catch (Exception e) {
            throw new BusinessException("环境配置的kubeConfig异常");
        }
        // 创建调度服务
        if (EmptyValidator.isEmpty(buildEnvironmentBO.getSpaceId())) {
            try {
                log.info("创建调度服务开始");
                createNamespace(client, "cmdevops-ci");
                createDeploymentWithOptionalSA(client, "cmdevops-ci", "cmdevops-ci-schedule-server", scheduleImage, 2, 8080, null, "1", "4", "1024Mi", "4096Mi");
                BuildEnvironmentBizServiceImpl.createNodePortService(client, "cmdevops-ci", "cmdevops-ci-schedule-server", "cmdevops-ci-schedule-server", "NodePort", 8080);
                log.info("创建调度服务结束");
                // 创建命名空间，cmdevops-ci
                // 循环创建 3个 cmdevops-ci-tool-xxx
                for (int i = 0; i < 3; i++) {
                    String namespace = "cmdevops-ci-tool-0" + (i + 1);
                    String sa = "cmdevops-ci-tool-sa";
                    // 创建命名空间
                    createNamespace(client, namespace);
                    createServiceAccountWithPodFullAccess(client, namespace, sa);
                    createDeploymentWithOptionalSA(client, namespace, "cmdevops-ci-tool-master", toolImage, 1, 8080, "cmdevops-ci-tool-sa", "1", "8", "2048Mi", "8192Mi");
                    BuildEnvironmentBizServiceImpl.createNodePortService(client, namespace, "ci-master", "cmdevops-ci-tool-master", "NodePort", 8080, 50000);
                    Map<String, String> data = new HashMap<>();
                    data.put("buildkitd.toml", buildkitdToml);
                    data.put("daemon.json", daemonJson);
                    BuildEnvironmentBizServiceImpl.createConfigMap(client, namespace, "inbound-agent-config", data);

                    BuildJenkinsNodeBO buildJenkinsNodeBO = new BuildJenkinsNodeBO();
                    buildJenkinsNodeBO.setName(namespace);
                    buildJenkinsNodeBO.setType("kubernetes");
                    buildJenkinsNodeBO.setNodeStatus("2");
                    buildJenkinsNodeBO.setNodeStartTime(LocalDateTime.now());
                    buildJenkinsNodeBO.setBuildEnvironmentId(buildEnvironmentBO.getId());
                    buildJenkinsNodeBO.setNodeUrl("http://ci-master." + namespace + ":8080");
                    buildJenkinsNodeBO.setTenantId(UserUtils.getTenantId());
                    buildJenkinsNodeBO.setCreateUid(UserUtils.getUserId());
                    buildJenkinsNodeBizService.createBuildJenkinsNode(buildJenkinsNodeBO);
                }
                BuildNodeBO buildNodeBO = new BuildNodeBO();
                buildNodeBO.setBuildEnvironmentId(buildEnvironmentBO.getId());
                buildNodeBO.setBuildNodeType("kubernetes");
                buildNodeBO.setBuildNodeName(buildEnvironmentBO.getEnvironmentName());
                buildNodeBO.setBuildNodeStatus("2");
                buildNodeBO.setTenantId(UserUtils.getTenantId());
                buildNodeBO.setCreateUid(UserUtils.getUserId());
                buildNodeBizService.createBuildNode(buildNodeBO);
            } catch (Exception e) {
                throw new BusinessException("缓存初始化异常");
            }
            buildEnvironmentBO.setSpaceId("init");
            BuildEnvironmentDO buildEnvironmentDO = BeanCloner.clone(buildEnvironmentBO, BuildEnvironmentDO.class);
            buildEnvironmentAtomService.updateById(buildEnvironmentDO);
        } else {
            throw new BusinessException("环境已初始化");
        }

    }

    // 集群采集
    @Override
    public void collectMetric() {
        // TODO 泽梁哥收集数据设计一下
        List<BuildEnvironmentDO> list = buildEnvironmentAtomService.list();
        String collectBatchId = System.currentTimeMillis() + "";
        list.stream().filter(buildEnvironmentDO -> buildEnvironmentDO.getSpaceId().equals("init")).forEach(buildEnvironmentDO -> {
            CiScheduleRequest ciScheduleRequest = new CiScheduleRequest();
            BuildScheduleSnapshotBO buildScheduleSnapshotBO = new BuildScheduleSnapshotBO();

            BuildJenkinsNodeBO buildJenkinsNodeBO = buildJenkinsNodeBizService.getBuildJenkinsNodeById("1");
            buildJenkinsNodeBO.setBuildEnvironmentId(buildEnvironmentDO.getId());
            List<BuildJenkinsNodeBO> buildJenkinsNodeBOS = buildJenkinsNodeBizService.list(buildJenkinsNodeBO);
            List<String> namespaceList = buildJenkinsNodeBOS.stream().map(BuildJenkinsNodeBO::getName).collect(Collectors.toList());

            buildScheduleSnapshotBO = BuildScheduleSnapshotBO.builder()
                    .ciScheduleUrl(buildEnvironmentDO.getEnvironmentUrl())
                    .s3Endpoint(buildEnvironmentDO.getCacheDir())
                    .accessKey(buildEnvironmentDO.getAccessKey())
                    .secretKey(buildEnvironmentDO.getSecretKey())
                    .namespaceList(namespaceList)
                    .build();
            ciScheduleRequest.setBuildTaskDTO(buildScheduleSnapshotBO);
            JSONObject metric = ciScheduleBizService.collectMetric(ciScheduleRequest);
            if (EmptyValidator.isNotEmpty(metric)) {
                String metricJson = metric.toJSONString();
                log.info(metricJson);
                new BuildEnvironmentMonitorDO().setEnvironmentId(buildEnvironmentDO.getId())
                        .setCollectBatchId(collectBatchId)
                        .setNamespace(JSONPath.read(metricJson, "$.k8sInfo[0].namespace", String.class))
                        .setHardLimitsCpu(JSONPath.read(metricJson, "$.k8sInfo[0].resourceQuotas[0].hard.limits.cpu", String.class))
                        .setHardLimitsMemory(JSONPath.read(metricJson, "$.k8sInfo[0].resourceQuotas[0].hard.limits.memory", String.class))
                        .setHardRequestsCpu(JSONPath.read(metricJson, "$.k8sInfo[0].resourceQuotas[0].hard.requests.cpu", String.class))
                        .setHardRequestsMemory(JSONPath.read(metricJson, "$.k8sInfo[0].resourceQuotas[0].hard.requests.memory", String.class))
                        .setUsedLimitsCpu(JSONPath.read(metricJson, "$.k8sInfo[0].resourceQuotas[0].used.limits.cpu", String.class))
                        .setUsedLimitsMemory(JSONPath.read(metricJson, "$.k8sInfo[0].resourceQuotas[0].used.limits.memory", String.class))
                        .setUsedRequestsCpu(JSONPath.read(metricJson, "$.k8sInfo[0].resourceQuotas[0].used.requests.cpu", String.class))
                        .setUsedRequestsMemory(JSONPath.read(metricJson, "$.k8sInfo[0].resourceQuotas[0].used.requests.memory", String.class))
                        .setPodCount(JSONPath.read(metricJson, "$.k8sInfo[0].resourceQuotas[0].podCount", String.class))
                        .setCreateTime(LocalDateTime.now())
                        .setCreateUid(UserUtils.getUserId())
                        .setSpaceId(buildEnvironmentDO.getSpaceId())
                        .setTenantId(UserUtils.getTenantId());
            }
            // JSONPath.read(json, "$.result.items[0].envDetailv0s[0]", null);
        });
    }

    private void createNamespace(ApiClient client, String name) throws Exception {

        Configuration.setDefaultApiClient(client);

        CoreV1Api api = new CoreV1Api();

        V1Namespace namespace = new V1Namespace()
                .metadata(new V1ObjectMeta().name(name));

        try {
            V1Namespace result = api.createNamespace(namespace).execute();
            log.info("✅ Namespace created: {}", result.getMetadata().getName());
        } catch (ApiException e) {
            if (e.getCode() == 409) {
                log.info("⚠️ Namespace already exists, skipping: {}", name);
            } else {
                log.error("❌ Failed to create Namespace: {}", e.getResponseBody(), e);
            }
        }
    }

    private void createServiceAccountWithPodFullAccess(ApiClient client, String namespace, String saName) throws Exception {
        Configuration.setDefaultApiClient(client);

        CoreV1Api coreV1Api = new CoreV1Api();
        RbacAuthorizationV1Api rbacApi = new RbacAuthorizationV1Api();

        // 1️⃣ 创建 ServiceAccount
        V1ServiceAccount serviceAccount = new V1ServiceAccount()
                .metadata(new V1ObjectMeta().name(saName).namespace(namespace));

        try {
            coreV1Api.createNamespacedServiceAccount(namespace, serviceAccount).execute();
            log.info("✅ ServiceAccount created: " + saName);
        } catch (ApiException e) {
            if (e.getCode() == 409) {
                log.info("⚠️ ServiceAccount already exists: " + saName);
            } else {
                throw e;
            }
        }

        // 2️⃣ 创建 Role —— 授权对 Pod 的所有操作
        String roleName = saName + "-pod-full-access";
        V1Role role = new V1Role()
                .metadata(new V1ObjectMeta().name(roleName).namespace(namespace))
                .rules(Collections.singletonList(
                        new V1PolicyRule()
                                .apiGroups(Collections.singletonList(""))      // Core API group
                                .resources(Collections.singletonList("pods"))  // pod 资源
                                .verbs(Collections.singletonList("*"))          // 所有操作
                ));
        try {
            rbacApi.createNamespacedRole(namespace, role).execute();
            log.info("✅ Role created: " + roleName);
        } catch (ApiException e) {
            if (e.getCode() == 409) {
                log.info("⚠️ Role already exists: " + roleName);
            } else {
                throw e;
            }
        }

        // 3️⃣ 创建 RoleBinding —— 绑定 Role 到 ServiceAccount
        String roleBindingName = saName + "-binding";
        V1RoleBinding roleBinding = new V1RoleBinding()
                .metadata(new V1ObjectMeta().name(roleBindingName).namespace(namespace))
                .roleRef(new V1RoleRef()
                        .apiGroup("rbac.authorization.k8s.io")
                        .kind("Role")
                        .name(roleName))
                .subjects(Collections.singletonList(
                        new RbacV1Subject()
                                .kind("ServiceAccount")
                                .namespace(namespace)
                                .name(saName)
                ));
        try {
            rbacApi.createNamespacedRoleBinding(namespace, roleBinding).execute();
            log.info("✅ RoleBinding created: " + roleBindingName);
        } catch (ApiException e) {
            if (e.getCode() == 409) {
                log.info("⚠️ RoleBinding already exists: " + roleBindingName);
            } else {
                throw e;
            }
        }
    }

    public void createDeploymentWithOptionalSA(
            ApiClient client,
            String namespace,
            String deploymentName,
            String image,
            int replicas,
            int containerPort,
            String serviceAccountName,
            String cpuRequest,
            String cpuLimit,
            String memoryRequest,
            String memoryLimit
    ) throws Exception {
        Configuration.setDefaultApiClient(client);

        CoreV1Api coreV1Api = new CoreV1Api();
        AppsV1Api appsV1Api = new AppsV1Api();

        boolean saExists = false;
        if (EmptyValidator.isNotEmpty(serviceAccountName)) {
            try {
                coreV1Api.readNamespacedServiceAccount(serviceAccountName, namespace).execute();
                saExists = true;
                log.info("✅ ServiceAccount 存在: " + serviceAccountName);
            } catch (io.kubernetes.client.openapi.ApiException e) {
                if (e.getCode() == 404) {
                    log.info("⚠️ ServiceAccount 不存在: " + serviceAccountName + "，将不绑定");
                } else {
                    throw e;
                }
            }
        }

        // 设置资源 requests/limits
        Map<String, Quantity> requests = new HashMap<>();
        Map<String, Quantity> limits = new HashMap<>();
        if (cpuRequest != null) requests.put("cpu", new Quantity(cpuRequest));
        if (memoryRequest != null) requests.put("memory", new Quantity(memoryRequest));
        if (cpuLimit != null) limits.put("cpu", new Quantity(cpuLimit));
        if (memoryLimit != null) limits.put("memory", new Quantity(memoryLimit));

        V1ResourceRequirements resources = new V1ResourceRequirements()
                .requests(requests)
                .limits(limits);

        V1Container container = new V1Container()
                .name(deploymentName + "-container")
                .image(image)
                .ports(Collections.singletonList(new V1ContainerPort().containerPort(containerPort)))
                .resources(resources);

        V1PodSpec podSpec = new V1PodSpec()
                .containers(Collections.singletonList(container));

        if (saExists) {
            podSpec.setServiceAccountName(serviceAccountName);
        }

        V1Deployment deployment = new V1Deployment()
                .apiVersion("apps/v1")
                .kind("Deployment")
                .metadata(new V1ObjectMeta().name(deploymentName).namespace(namespace))
                .spec(new V1DeploymentSpec()
                        .replicas(replicas)
                        .selector(new V1LabelSelector().matchLabels(Collections.singletonMap("app", deploymentName)))
                        .template(new V1PodTemplateSpec()
                                .metadata(new V1ObjectMeta().labels(Collections.singletonMap("app", deploymentName)))
                                .spec(podSpec)
                        )
                );

        try {
            V1Deployment created = appsV1Api.createNamespacedDeployment(namespace, deployment)
                    .pretty("true")
                    .execute();
            log.info("🎉 Deployment created: " + created.getMetadata().getName());
        } catch (io.kubernetes.client.openapi.ApiException e) {
            if (e.getCode() == 409) {
                log.info("⚠️ Deployment 已存在，跳过创建: " + deploymentName);
            } else {
                log.error("❌ 创建 Deployment 异常: " + e.getResponseBody(), e);
                throw e;
            }
        }
    }

    public static void createNodePortService(ApiClient client, String namespace, String serviceName, String selectorLabel, String svcType, int... ports) throws Exception {
        Configuration.setDefaultApiClient(client);

        CoreV1Api coreV1Api = new CoreV1Api();

        V1ServicePort[] servicePorts = new V1ServicePort[ports.length];

        for (int i = 0; i < ports.length; i++) {
            int port = ports[i];
            String portName = "port-" + port;

            V1ServicePort servicePort = new V1ServicePort()
                    .name(portName)
                    .port(port)
                    .targetPort(new IntOrString(port));

            servicePorts[i] = servicePort;
        }

        V1Service service = new V1Service()
                .apiVersion("v1")
                .kind("Service")
                .metadata(new V1ObjectMeta()
                        .name(serviceName)
                        .namespace(namespace))
                .spec(new V1ServiceSpec()
                        .type("NodePort")
                        .ports(Arrays.asList(servicePorts))
                        .selector(Collections.singletonMap("app", selectorLabel))
                );

        try {
            coreV1Api.createNamespacedService(namespace, service).pretty("true").execute();
            log.info("✅ NodePort Service created: " + serviceName);
        } catch (ApiException e) {
            if (e.getCode() == 409) {
                log.info("⚠️ Service already exists, skipping creation: " + serviceName);
                // 跳过，不执行任何操作
            } else {
                log.error("❌ Error creating Service: " + e.getResponseBody(), e);
                throw e; // 或根据业务需求决定是否继续抛出异常
            }
        }
    }


    public static void createConfigMap(ApiClient client, String namespace, String configMapName, Map<String, String> data) throws Exception {
        Configuration.setDefaultApiClient(client);

        CoreV1Api coreV1Api = new CoreV1Api();

        V1ConfigMap configMap = new V1ConfigMap()
                .apiVersion("v1")
                .kind("ConfigMap")
                .metadata(new V1ObjectMeta().name(configMapName).namespace(namespace))
                .data(data);

        try {
            coreV1Api.createNamespacedConfigMap(namespace, configMap).pretty("true").execute();
            log.info("✅ ConfigMap created: " + configMapName);
        } catch (ApiException e) {
            if (e.getCode() == 409) { // Resource conflict, indicates that the resource already exists
                log.warn("⚠️ ConfigMap " + configMapName + " already exists. Attempting update...");
                // Perform update logic here, for example:
                coreV1Api.replaceNamespacedConfigMap(configMapName, namespace, configMap).pretty("true").execute();
                log.info("✅ ConfigMap updated: " + configMapName);
            } else {
                log.error("❌ Error creating/updating ConfigMap: " + e.getResponseBody());
                // Handle other exceptions as needed
            }
        }
    }


}
