package com.cmcc.cmdevops.ci.service.atom.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cmcc.cmdevops.ci.service.atom.BuildEnvironmentMonitorAtomService;
import com.cmcc.cmdevops.ci.service.dao.BuildEnvironmentMonitorDO;
import com.cmcc.cmdevops.ci.service.dao.mapper.BuildEnvironmentMonitorMapper;
import org.springframework.stereotype.Service;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR> @since 2025-05-22
 */
@Service
public class BuildEnvironmentMonitorAtomServiceImpl extends ServiceImpl<BuildEnvironmentMonitorMapper, BuildEnvironmentMonitorDO> implements BuildEnvironmentMonitorAtomService {

}
