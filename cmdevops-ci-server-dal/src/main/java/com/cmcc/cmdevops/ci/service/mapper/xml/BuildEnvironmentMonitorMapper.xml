<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cmcc.cmdevops.ci.service.dao.mapper.BuildEnvironmentMonitorMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.cmcc.cmdevops.ci.service.dao.BuildEnvironmentMonitorDO">
        <id column="id" property="id" />
        <result column="environment_id" property="environmentId" />
        <result column="collect_batch_id" property="collectBatchId" />
        <result column="namespace" property="namespace" />
        <result column="hard_limits_cpu" property="hardLimitsCpu" />
        <result column="hard_limits_memory" property="hardLimitsMemory" />
        <result column="hard_requests_cpu" property="hardRequestsCpu" />
        <result column="hard_requests_memory" property="hardRequestsMemory" />
        <result column="used_limits_cpu" property="usedLimitsCpu" />
        <result column="used_limits_memory" property="usedLimitsMemory" />
        <result column="used_requests_cpu" property="usedRequestsCpu" />
        <result column="used_requests_memory" property="usedRequestsMemory" />
        <result column="pod_count" property="podCount" />
        <result column="space_id" property="spaceId" />
        <result column="tenant_id" property="tenantId" />
        <result column="deleted" property="deleted" />
        <result column="create_uid" property="createUid" />
        <result column="create_time" property="createTime" />
        <result column="update_uid" property="updateUid" />
        <result column="update_time" property="updateTime" />
        <result column="delete_uid" property="deleteUid" />
        <result column="delete_time" property="deleteTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, environment_id, collect_batch_id, namespace, hard_limits_cpu, hard_limits_memory, hard_requests_cpu, hard_requests_memory,
        used_limits_cpu, used_limits_memory, used_requests_cpu, used_requests_memory, pod_count, space_id, tenant_id, deleted,
        create_time, create_uid, update_time, update_uid, delete_time, delete_uid
    </sql>

</mapper>
