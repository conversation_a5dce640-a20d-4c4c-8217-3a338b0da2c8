package com.cmcc.cmdevops.ci.service.dao;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

@Getter
@Setter
@Accessors(chain = true)
@TableName("build_environment_monitor")
public class BuildEnvironmentMonitorDO implements Serializable {
    @Serial
    private static final long serialVersionUID = -3815403250974624038L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @TableField("environment_id")
    private Integer environmentId;

    /**
     * 采集批次，同一批次下存在多个namespace
     */
    @TableField("collect_batch_id")
    private String collectBatchId;

    /**
     * 命名空间
     */
    @TableField("namespace")
    private String namespace;

    /**
     * 最大CPU
     */
    @TableField("hard_limits_cpu")
    private String hardLimitsCpu;

    /**
     * 最大内存
     */
    @TableField("hard_limits_memory")
    private String hardLimitsMemory;

    /**
     * 最大申请CPU
     */
    @TableField("hard_requests_cpu")
    private String hardRequestsCpu;

    /**
     * 最大申请内存
     */
    @TableField("hard_requests_memory")
    private String hardRequestsMemory;

    /**
     * 最大使用CPU
     */
    @TableField("used_limits_cpu")
    private String usedLimitsCpu;

    /**
     * 最大使用内存
     */
    @TableField("used_limits_memory")
    private String usedLimitsMemory;

    /**
     * 最大申请使用CPU
     */
    @TableField("used_requests_cpu")
    private String usedRequestsCpu;

    /**
     * 最大申请使用内存
     */
    @TableField("used_requests_memory")
    private String usedRequestsMemory;

    /**
     * pod数，即运行中任务数
     */
    @TableField("pod_count")
    private String podCount;

    @TableField("space_id")
    private String spaceId;

    @TableField("tenant_id")
    private String tenantId;

    @TableField("deleted")
    @TableLogic
    private Boolean deleted;

    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @TableField("create_uid")
    private String createUid;

    @TableField("update_time")
    private LocalDateTime updateTime;

    @TableField("update_uid")
    private String updateUid;

    @TableField("delete_time")
    private LocalDateTime deleteTime;

    @TableField("delete_uid")
    private String deleteUid;
}
