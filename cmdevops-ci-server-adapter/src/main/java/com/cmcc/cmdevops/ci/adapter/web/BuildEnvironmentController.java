package com.cmcc.cmdevops.ci.adapter.web;

import com.cmcc.cmdevops.BaseResponse;
import com.cmcc.cmdevops.PageResponse;
import com.cmcc.cmdevops.ci.adapter.dto.BuildEnvironmentDTO;
import com.cmcc.cmdevops.ci.adapter.dto.BuildNodeDTO;
import com.cmcc.cmdevops.ci.adapter.dto.request.BuildEnvironmentPageRequest;
import com.cmcc.cmdevops.ci.service.bo.BuildEnvironmentBO;
import com.cmcc.cmdevops.ci.service.bo.BuildNodeBO;
import com.cmcc.cmdevops.ci.service.business.BuildEnvironmentBizService;
import com.cmcc.cmdevops.ci.service.business.BuildNodeBizService;
import com.cmcc.cmdevops.util.BeanCloner;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;
import java.util.List;

/**
 * 注意这是已经使用swagger3.0版本
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/v1/environment")
@RequiredArgsConstructor
public class BuildEnvironmentController {
    private static final Logger LOGGER = LoggerFactory.getLogger(BuildEnvironmentController.class);

    private final BuildEnvironmentBizService buildEnvironmentBizService;
    private final BuildNodeBizService buildNodeBizService;

    @GetMapping("/page")
    public PageResponse<List<BuildEnvironmentDTO>> page(BuildEnvironmentPageRequest pageRequest) {
        BuildEnvironmentBO buildEnvironmentBO = BeanCloner.clone(pageRequest.getBuildEnvironmentDTO(), BuildEnvironmentBO.class);
        PageResponse<List<BuildEnvironmentBO>> pageResponse = buildEnvironmentBizService.page(pageRequest, buildEnvironmentBO);
        if (pageResponse == null) {
            return PageResponse.success(Collections.emptyList(), 0L, pageRequest.getPageNo(), pageRequest.getPageSize());
        }
        List<BuildEnvironmentDTO> list = BeanCloner.clone(pageResponse.getData(), BuildEnvironmentDTO.class);
        return PageResponse.success(list, pageResponse.getCount(), pageRequest.getPageNo(), pageRequest.getPageSize());
    }

    @PostMapping("/save")
    public BaseResponse<Void> save(@RequestBody BuildEnvironmentDTO dto) {
        BuildEnvironmentBO buildEnvironmentBO = BeanCloner.clone(dto, BuildEnvironmentBO.class);
        buildEnvironmentBizService.save(buildEnvironmentBO);
        return BaseResponse.success();
    }

    @PutMapping("/update")
    public BaseResponse<Void> update(@RequestBody BuildEnvironmentDTO dto) {
        BuildEnvironmentBO buildEnvironmentBO = BeanCloner.clone(dto, BuildEnvironmentBO.class);
        buildEnvironmentBizService.update(buildEnvironmentBO);
        return BaseResponse.success();
    }

    @DeleteMapping("/delete/{id}")
    public BaseResponse<Void> delete(@PathVariable Integer id) {
        buildEnvironmentBizService.delete(id);
        return BaseResponse.success();
    }

    @GetMapping("/detail/{id}")
    public BaseResponse<BuildEnvironmentDTO> detail(@PathVariable Integer id) {
        BuildEnvironmentDTO buildEnvironmentDTO = BeanCloner.clone(buildEnvironmentBizService.detail(id), BuildEnvironmentDTO.class);
        return BaseResponse.success(buildEnvironmentDTO);
    }

    // 清理緩存
    @GetMapping("/init/{envId}")
    public BaseResponse<String> cleanCache(@PathVariable Integer envId) {
        buildEnvironmentBizService.init(envId);
        return BaseResponse.success();
    }

    @GetMapping("/buildNode")
    public BaseResponse<List<BuildNodeDTO>> buildNodeList() {
        List<BuildNodeBO> list = buildNodeBizService.list(new BuildNodeBO());
        return BaseResponse.success(BeanCloner.clone(list, BuildNodeDTO.class));
    }

    @GetMapping("/collectMetric")
    public BaseResponse<String> collectMetric() {
        buildEnvironmentBizService.collectMetric();
        return BaseResponse.success("xxx");
    }
}
